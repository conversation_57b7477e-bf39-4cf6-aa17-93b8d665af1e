using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Configuration;
using Microsoft.Graph;
using Microsoft.Graph.Models;
using System.Net;
using System.Text.Json;
using System.ComponentModel.DataAnnotations;
using PasswordHistoryValidator.Services;
using PasswordHistoryValidator.Shared;
using Azure.Identity;
using System.Collections.Generic;
using System.Linq;

namespace PasswordHistoryValidator;

public class RegistrationFunction : BaseFunctionService
{
    private readonly ILogger<RegistrationFunction> _logger;
    private readonly IPasswordHistoryService _passwordHistoryService;
    private readonly GraphServiceClient _graphServiceClient;
    private readonly IConfiguration _configuration;
    private readonly EntraOptions _entraOptions;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly InvitationTokenManager _invitationTokenManager;
    private readonly AntiSpamService _antiSpamService;

    public RegistrationFunction(
        ILogger<RegistrationFunction> logger,
        IPasswordHistoryService passwordHistoryService,
        GraphServiceClient graphServiceClient,
        IConfiguration configuration,
        IOptions<EntraOptions> entraOptions,
        IHttpClientFactory httpClientFactory,
        InvitationTokenManager invitationTokenManager,
        AntiSpamService antiSpamService,
        JsonSerializerOptions jsonOptions) : base(jsonOptions)
    {
        _logger = logger;
        _passwordHistoryService = passwordHistoryService;
        _graphServiceClient = graphServiceClient;
        _configuration = configuration;
        _entraOptions = entraOptions.Value;
        _httpClientFactory = httpClientFactory;
        _invitationTokenManager = invitationTokenManager;
        _antiSpamService = antiSpamService;
    }

    [Function("RegistrationService")]
    public async Task<HttpResponseData> Run(
        [HttpTrigger(AuthorizationLevel.Function, "post", "options")] HttpRequestData req,
        CancellationToken cancellationToken)
    {
        var correlationId = GenerateCorrelationId();

        if (req.Method.Equals("OPTIONS", StringComparison.OrdinalIgnoreCase))
        {
            return CreateCorsResponse(req);
        }

        try
        {
            var operation = req.Query["operation"];
            if (string.IsNullOrEmpty(operation))
            {
                return await CreateErrorResponse(req, "Operation parameter required", correlationId);
            }

            return operation.ToLower() switch
            {
                "register" => await HandleUserRegistration(req, correlationId, cancellationToken),
                _ => await CreateErrorResponse(req, $"Invalid operation: {operation}", correlationId)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Registration service error [CorrelationId: {CorrelationId}]", correlationId);
            return await CreateErrorResponse(req, "Service error", correlationId);
        }
    }

    private async Task<HttpResponseData> HandleUserRegistration(HttpRequestData req, string correlationId, CancellationToken cancellationToken)
    {
        try
        {
            var requestBody = await new StreamReader(req.Body).ReadToEndAsync();

            // Deserialize as InvitationRequest (required for registration)
            InvitationRequest? invitationData = null;

            try
            {
                invitationData = JsonSerializer.Deserialize<InvitationRequest>(requestBody, JsonOptions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to parse invitation request [CorrelationId: {CorrelationId}]", correlationId);
                return await CreateErrorResponse(req, "Invalid request format", correlationId);
            }

            // Validate we have valid invitation data
            if (invitationData == null)
            {
                return await CreateErrorResponse(req, "Invalid request data", correlationId);
            }

            // Require invitation verification code for all registrations
            if (string.IsNullOrEmpty(invitationData.VerificationCode))
            {
                return await CreateErrorResponse(req, "Invitation code required for registration", correlationId);
            }

            // Validate the invitation request data
            var validationResults = new List<ValidationResult>();
            var validationContext = new ValidationContext(invitationData);
            if (!Validator.TryValidateObject(invitationData, validationContext, validationResults, true))
            {
                var errors = string.Join(", ", validationResults.Select(r => r.ErrorMessage));
                return await CreateErrorResponse(req, $"Validation failed: {errors}", correlationId);
            }

            // Validate invitation code
            var (isValid, tokenData, errorMessage) = string.IsNullOrEmpty(invitationData.Token)
                ? await _invitationTokenManager.ValidateInvitationByCode(invitationData.VerificationCode)
                : await _invitationTokenManager.ValidateInvitationToken(invitationData.Token, invitationData.VerificationCode);

            if (!isValid)
            {
                _logger.LogWarning("Invalid invitation token for {Email}: {ErrorMessage} [CorrelationId: {CorrelationId}]",
                    invitationData.Email, errorMessage, correlationId);
                return await CreateErrorResponse(req, errorMessage, correlationId);
            }

            if (tokenData == null)
            {
                return await CreateErrorResponse(req, "Invalid invitation token data", correlationId);
            }

            // Verify email matches token
            if (!string.Equals(tokenData.Email, invitationData.Email, StringComparison.OrdinalIgnoreCase))
            {
                _logger.LogWarning("Email mismatch in invitation token for {Email} [CorrelationId: {CorrelationId}]",
                    invitationData.Email, correlationId);
                return await CreateErrorResponse(req, "Email address does not match invitation", correlationId);
            }

            // Create unified data object for the rest of the method
            var data = new AuthRequest
            {
                Email = invitationData.Email,
                Password = invitationData.Password ?? string.Empty,
                FirstName = invitationData.FirstName,
                LastName = invitationData.LastName,
                ApplicationName = tokenData.ApplicationId
            };

            // Simple anti-spam protection (1-second cooldown)
            var clientId = BaseFunctionService.GetClientIdentifier(req);
            if (!_antiSpamService.IsRequestAllowed(clientId))
            {
                return await CreateErrorResponse(req, "Please wait a moment before trying again.", "TooManyRequests", HttpStatusCode.TooManyRequests, correlationId);
            }


            try
            {
                var existingUsers = await _graphServiceClient.Users
                    .GetAsync(requestConfiguration =>
                    {
                        requestConfiguration.QueryParameters.Filter =
                            $"mail eq '{data.Email}' or userPrincipalName eq '{data.Email}' or proxyAddresses/any(c:c eq 'SMTP:{data.Email}')";
                        requestConfiguration.QueryParameters.Select = new[] { "id", "mail", "userPrincipalName", "displayName" };
                    }, cancellationToken);

                if (existingUsers?.Value?.Any() == true)
                {
                    return await CreateErrorResponse(req, "User already exists", correlationId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "User check error [CorrelationId: {CorrelationId}]", correlationId);
                return await CreateErrorResponse(req, "Error checking user existence", correlationId);
            }

            try
            {
                var passwordValidation = await _passwordHistoryService.ValidatePasswordAgainstHistoryAsync(
                    data.ApplicationName ?? "Default Application", data.Email, data.Password, cancellationToken);

                if (!passwordValidation.IsSuccess)
                {
                    return await CreateErrorResponse(req, passwordValidation.ErrorMessage, correlationId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Password validation error [CorrelationId: {CorrelationId}]", correlationId);
                return await CreateErrorResponse(req, "Password validation error", correlationId);
            }

            var applicationName = data.ApplicationName ?? "Default Application";
            try
            {
                var existingUsers = await _graphServiceClient.Users
                    .GetAsync(requestConfiguration =>
                    {
                        requestConfiguration.QueryParameters.Filter =
                            $"(mail eq '{data.Email}' or userPrincipalName eq '{data.Email}' or proxyAddresses/any(c:c eq 'SMTP:{data.Email}')) and startswith(department, '{applicationName}')";
                        requestConfiguration.QueryParameters.Select = new[] { "id", "mail", "userPrincipalName", "displayName", "department" };
                    }, cancellationToken);

                if (existingUsers?.Value != null && existingUsers.Value.Count > 0)
                {
                    _logger.LogWarning("Registration blocked - email {Email} already exists [CorrelationId: {CorrelationId}]", data.Email, correlationId);

                    foreach (var existingUser in existingUsers.Value)
                    {
                        _logger.LogInformation("Existing user in app: ID={UserId}, Email={Email}, UPN={UPN}, DisplayName={DisplayName}, Department={Department}",
                            existingUser.Id, existingUser.Mail, existingUser.UserPrincipalName, existingUser.DisplayName, existingUser.Department);
                    }

                    return await CreateErrorResponse(req, $"A user with this email address already exists in application '{applicationName}'. Please use a different email or try logging in.", correlationId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking for existing users during registration [CorrelationId: {CorrelationId}]", correlationId);
            }

            try
            {
                var upn = $"{Guid.NewGuid()}@{_entraOptions.DefaultDomain}";
                var displayNameWithContext = $"{data.FirstName} {data.LastName} ({applicationName})";
                var applicationContext = applicationName;
                if (!string.IsNullOrWhiteSpace(data.Department))
                {
                    applicationContext += $" - {data.Department}";
                }

                var newUser = new User
                {
                    DisplayName = displayNameWithContext,
                    GivenName = data.FirstName,
                    Surname = data.LastName,
                    Mail = data.Email,
                    UserPrincipalName = upn,
                    Department = applicationContext,
                    Identities = new List<ObjectIdentity>
                    {
                        new ObjectIdentity
                        {
                            SignInType = "emailAddress",
                            Issuer = _entraOptions.DefaultDomain,
                            IssuerAssignedId = data.Email
                        }
                    },
                    PasswordProfile = new PasswordProfile
                    {
                        Password = data.Password,
                        ForceChangePasswordNextSignIn = false
                    },
                    AccountEnabled = true
                };

                var createdUser = await _graphServiceClient.Users.PostAsync(newUser, cancellationToken: cancellationToken);

                if (createdUser?.Id == null)
                {
                    return await CreateErrorResponse(req, "Failed to create user", correlationId);
                }

                // Mark invitation token as used
                var tokenMarked = await _invitationTokenManager.MarkTokenAsUsed(invitationData.Token ?? string.Empty);
                if (!tokenMarked)
                {
                    _logger.LogWarning("Failed to mark invitation token as used for {Email} [CorrelationId: {CorrelationId}]",
                        data.Email, correlationId);
                }

                _logger.LogInformation("User created successfully: {UserId} for {Email} [CorrelationId: {CorrelationId}]",
                    createdUser.Id, data.Email, correlationId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "User creation error [CorrelationId: {CorrelationId}]: {ErrorMessage}", correlationId, ex.Message);

                var userCreationErrorMessage = ex.Message.Contains("already exists") || ex.Message.Contains("duplicate")
                    ? "A user with this email address already exists. Please use a different email or try logging in."
                    : "Error creating user account";

                return await CreateErrorResponse(req, userCreationErrorMessage, correlationId);
            }

            try
            {
                var historyUpdate = await _passwordHistoryService.UpdatePasswordHistoryAsync(
                    data.ApplicationName ?? "Default Application", data.Email, data.Password, cancellationToken);

                if (!historyUpdate.IsSuccess)
                {
                    _logger.LogError("Password history update failed for {Email} [CorrelationId: {CorrelationId}]", data.Email, correlationId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Password history error for {Email} [CorrelationId: {CorrelationId}]", data.Email, correlationId);
            }

            return await CreateSuccessResponse<object>(req, "User account created successfully", null, HttpStatusCode.OK, correlationId);

        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Registration error [CorrelationId: {CorrelationId}]", correlationId);
            return await CreateErrorResponse(req, "Registration failed", correlationId);
        }
    }
}
