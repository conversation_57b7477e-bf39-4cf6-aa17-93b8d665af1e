using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Http.Resilience;
using Azure.Storage.Blobs;
using Azure.Identity;
using Microsoft.Graph;
using System.Text.Json;
using PasswordHistoryValidator.Services;
using PasswordHistoryValidator.Shared;

namespace PasswordHistoryValidator.Extensions;

public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Registers all strongly-typed configuration options
    /// </summary>
    public static IServiceCollection AddConfigurationOptions(this IServiceCollection services, IConfiguration configuration)
    {
        // Configure strongly-typed options
        services.Configure<SendGridOptions>(configuration.GetSection(SendGridOptions.SectionName));
        services.Configure<EntraOptions>(options =>
        {
            options.ClientId = configuration["EntraExternalID:ClientId"] ?? string.Empty;
            options.ClientSecret = configuration["EntraExternalID:ClientSecret"] ?? string.Empty;
            options.TenantId = configuration["EntraExternalID:TenantId"] ?? string.Empty;
            options.DefaultDomain = configuration["EntraExternalID:DefaultDomain"] ?? "yourtenant.onmicrosoft.com";
        });
        services.Configure<PasswordResetOptions>(configuration.GetSection(PasswordResetOptions.SectionName));
        services.Configure<AccountRegistrationOptions>(configuration.GetSection(AccountRegistrationOptions.SectionName));
        services.Configure<StorageOptions>(options =>
        {
            // Use AzureWebJobsStorage as primary (always available in Azure Functions)
            // Fall back to Storage:ConnectionString for local development
            options.ConnectionString = configuration["AzureWebJobsStorage"] ?? configuration["Storage:ConnectionString"] ?? string.Empty;
        });
        services.Configure<InvitationOptions>(configuration.GetSection(InvitationOptions.SectionName));

        return services;
    }

    /// <summary>
    /// Registers Azure services (Blob Storage, Graph API, etc.)
    /// </summary>
    public static IServiceCollection AddAzureServices(this IServiceCollection services)
    {
        // Azure Blob Storage client
        services.AddSingleton<BlobServiceClient>(serviceProvider =>
        {
            var storageOptions = serviceProvider.GetRequiredService<IOptions<StorageOptions>>();
            var connectionString = storageOptions.Value.ConnectionString;

            if (string.IsNullOrEmpty(connectionString))
            {
                throw new InvalidOperationException("Storage:ConnectionString is required in configuration. Please configure this value in local.settings.json.");
            }

            return new BlobServiceClient(connectionString);
        });

        // Configure HTTP client with resilience patterns for Graph API
        services.AddHttpClient("GraphAPI")
            .AddStandardResilienceHandler(options =>
            {
                // Circuit breaker: Protects Graph API from being overwhelmed
                options.CircuitBreaker.FailureRatio = 0.5; // Open when 50% of requests fail
                options.CircuitBreaker.SamplingDuration = TimeSpan.FromSeconds(30);
                options.CircuitBreaker.BreakDuration = TimeSpan.FromSeconds(60);

                // Retry: Handle transient failures with exponential backoff
                options.Retry.MaxRetryAttempts = 3;
                options.Retry.Delay = TimeSpan.FromSeconds(2);

                // Timeout: Prevent hanging requests (Graph operations can take time)
                options.TotalRequestTimeout.Timeout = TimeSpan.FromSeconds(45);
            });

        // Graph API client using the resilient HTTP client
        services.AddScoped<GraphServiceClient>(serviceProvider =>
        {
            var entraOptions = serviceProvider.GetRequiredService<IOptions<EntraOptions>>();
            var httpClientFactory = serviceProvider.GetRequiredService<IHttpClientFactory>();
            var clientId = entraOptions.Value.ClientId;
            var clientSecret = entraOptions.Value.ClientSecret;
            var tenantId = entraOptions.Value.TenantId;

            if (string.IsNullOrEmpty(clientId) || string.IsNullOrEmpty(clientSecret) || string.IsNullOrEmpty(tenantId))
            {
                throw new InvalidOperationException("EntraExternalID configuration (ClientId, ClientSecret, TenantId) is required in configuration. Please configure these values in local.settings.json.");
            }

            var credential = new ClientSecretCredential(tenantId, clientId, clientSecret);
            var httpClient = httpClientFactory.CreateClient("GraphAPI");
            return new GraphServiceClient(httpClient, credential);
        });

        return services;
    }

    /// <summary>
    /// Registers business logic services
    /// </summary>
    public static IServiceCollection AddBusinessServices(this IServiceCollection services)
    {
        // Core business services
        services.AddScoped<IPasswordHistoryService, PasswordHistoryService>();
        services.AddScoped<IEmailService, EmailService>();
        
        // Token management services
        services.AddScoped<ResetTokenManager>();
        services.AddScoped<InvitationTokenManager>();
        
        // Anti-spam service
        services.AddSingleton<AntiSpamService>();

        return services;
    }

    /// <summary>
    /// Registers Azure Function classes
    /// </summary>
    public static IServiceCollection AddFunctionServices(this IServiceCollection services)
    {
        services.AddScoped<PasswordHistoryValidator.AuthenticationFunction>();
        services.AddScoped<PasswordHistoryValidator.PasswordFunction>();
        services.AddScoped<PasswordHistoryValidator.UtilityFunction>();
        services.AddScoped<PasswordHistoryValidator.InvitationFunction>();
        services.AddScoped<PasswordHistoryValidator.RegistrationFunction>();

        return services;
    }

    /// <summary>
    /// Registers common infrastructure services (JSON, caching, etc.)
    /// </summary>
    public static IServiceCollection AddInfrastructureServices(this IServiceCollection services)
    {
        // Memory cache
        services.AddMemoryCache();
        
        // HTTP client factory
        services.AddHttpClient();

        // JSON serializer options as singleton
        services.AddSingleton<JsonSerializerOptions>(provider => new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            PropertyNameCaseInsensitive = true,
            WriteIndented = true
        });

        return services;
    }
}
