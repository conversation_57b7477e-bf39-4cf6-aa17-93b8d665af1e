function buildInvitationServiceUrl() {
  const baseUrl = window.appConfig?.functionUrl;
  const functionKey = window.appConfig?.invitationFunctionKey;

  if (!baseUrl || baseUrl.includes('ERROR_MISSING') || !functionKey || functionKey.includes('ERROR_MISSING')) {
    return null;
  }

  return `${baseUrl}/api/InvitationService?operation=invite-user&code=${functionKey}`;
}

function validateEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email.trim());
}

function validateName(name) {
  const trimmed = name.trim();
  return trimmed.length >= 1 && trimmed.length <= 50 && /^[a-zA-Z\s'-]+$/.test(trimmed);
}

// DOM Elements
const errorMessage = $('#errorMessage');
const successMessage = $('#successMessage');
const invitationForm = $('#invitationForm');
const sendButton = $('#sendButton');
const recentInvitations = $('#recentInvitations');

function showMessage(message, isError = true) {
  errorMessage.addClass('d-none');
  successMessage.addClass('d-none');
  
  if (isError) {
    $('#errorText').text(message);
    errorMessage.removeClass('d-none');
  } else {
    $('#successText').text(message);
    successMessage.removeClass('d-none');
  }
}

function showLoadingState(message) {
  sendButton.prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> ' + message);
}

function resetLoadingState() {
  sendButton.prop('disabled', false).html('<i class="fas fa-paper-plane me-2"></i>Send Invitation');
}

async function sendInvitation(invitationData) {
  showLoadingState('Sending Invitation...');

  try {
    const email = invitationData.email.trim();
    const firstName = invitationData.firstName.trim();
    const lastName = invitationData.lastName.trim();

    if (!validateEmail(email)) {
      throw new Error('Invalid email format');
    }

    if (!validateName(firstName)) {
      throw new Error('Invalid first name');
    }

    if (!validateName(lastName)) {
      throw new Error('Invalid last name');
    }

    const invitationServiceUrl = buildInvitationServiceUrl();
    if (!invitationServiceUrl) {
      throw new Error('Azure Function configuration missing. Please check Power Pages settings.');
    }

    const requestBody = {
      Email: email,
      FirstName: firstName,
      LastName: lastName,
      ApplicationName: window.appConfig?.applicationName
    };

    const response = await fetch(invitationServiceUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Client-Version': '3.0.0-simplified',
        'X-Requested-With': 'XMLHttpRequest'
      },
      body: JSON.stringify(requestBody)
    });

    if (response.status === 429) {
      throw new Error('Too many requests. Please wait a moment before trying again.');
    }

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `Server error: ${response.status}`);
    }

    const result = await response.json();

    const isSuccess = response.ok && result.success === true;

    if (!isSuccess) {
      if (response.status === 429 || result.errorCode === 'RateLimitExceeded') {
        const retryMessage = result.retryAfter ? ` Please try again after ${new Date(result.retryAfter).toLocaleTimeString()}.` : ' Please try again later.';
        throw new Error((result.message || 'Rate limit exceeded.') + retryMessage);
      }
      throw new Error(result.message || 'Failed to send invitation');
    }

    return {
      success: true,
      message: result.message || 'Invitation sent successfully'
    };
  } catch (error) {
    throw error;
  } finally {
    resetLoadingState();
  }
}

function validateForm() {
  let isValid = true;
  
  $('.form-control').removeClass('is-invalid');
  $('.invalid-feedback').text('');

  const email = $('#email').val();
  if (!validateEmail(email)) {
    $('#emailError').text('Please enter a valid email address');
    $('#email').addClass('is-invalid');
    isValid = false;
  }

  const firstName = $('#firstName').val();
  if (!validateName(firstName)) {
    $('#firstNameError').text('Please enter a valid first name (letters, spaces, hyphens, and apostrophes only)');
    $('#firstName').addClass('is-invalid');
    isValid = false;
  }

  const lastName = $('#lastName').val();
  if (!validateName(lastName)) {
    $('#lastNameError').text('Please enter a valid last name (letters, spaces, hyphens, and apostrophes only)');
    $('#lastName').addClass('is-invalid');
    isValid = false;
  }

  return isValid;
}

function addToRecentInvitations(email, firstName, lastName) {
  const timestamp = new Date().toLocaleString();
  const invitationHtml = `
    <div class="d-flex justify-content-between align-items-center py-1">
      <span><strong>${firstName} ${lastName}</strong> (${email})</span>
      <small class="text-muted">${timestamp}</small>
    </div>
  `;
  
  if (recentInvitations.text().includes('No recent invitations')) {
    recentInvitations.html(invitationHtml);
  } else {
    recentInvitations.prepend(invitationHtml);
  }
  
  // Keep only the last 5 invitations
  const invitations = recentInvitations.children();
  if (invitations.length > 5) {
    invitations.slice(5).remove();
  }
}

function initializeFormHandlers() {
  invitationForm.submit(async function(event) {
    event.preventDefault();
    showMessage('', false);

    try {
      if (!validateForm()) {
        return;
      }

      const invitationData = {
        email: $('#email').val(),
        firstName: $('#firstName').val(),
        lastName: $('#lastName').val()
      };

      // Store current invitation data for potential overwrite
      currentInvitationData = invitationData;

      const result = await sendInvitation(invitationData);

      if (result.success) {
        showMessage(`Invitation sent successfully to ${invitationData.email}!`, false);
        addToRecentInvitations(invitationData.email, invitationData.firstName, invitationData.lastName);
        invitationForm[0].reset();
        currentInvitationData = null;
      }

    } catch (error) {
      showMessage(error.message || "An unexpected error occurred. Please try again.");
    }
  });
}



$(document).ready(function() {
  initializeConfiguration();
  initializeFormHandlers();
});
