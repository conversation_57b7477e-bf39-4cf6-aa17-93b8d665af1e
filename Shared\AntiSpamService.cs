using System.Collections.Concurrent;

namespace PasswordHistoryValidator.Shared;

/// <summary>
/// Simple anti-spam protection to prevent accidental double-clicks.
/// Much simpler than the previous rate limiting system - just prevents rapid successive requests.
/// </summary>
public class AntiSpamService
{
    private readonly ConcurrentDictionary<string, DateTime> _lastRequestTimes = new();
    private readonly TimeSpan _cooldownPeriod = TimeSpan.FromSeconds(1);

    /// <summary>
    /// Checks if a request should be allowed based on simple cooldown logic.
    /// </summary>
    /// <param name="clientId">Client identifier (IP address or similar)</param>
    /// <returns>True if request is allowed, false if still in cooldown period</returns>
    public bool IsRequestAllowed(string clientId)
    {
        var now = DateTime.UtcNow;
        
        // Get or add the last request time for this client
        var lastRequestTime = _lastRequestTimes.AddOrUpdate(
            clientId,
            now, // If new client, set current time
            (key, existingTime) =>
            {
                // If enough time has passed, update to current time
                if (now - existingTime >= _cooldownPeriod)
                {
                    return now;
                }
                // Otherwise, keep the existing time (request will be rejected)
                return existingTime;
            });

        // Request is allowed if we just updated the time to now
        return lastRequestTime == now;
    }

    /// <summary>
    /// Cleanup old entries to prevent memory leaks.
    /// Called periodically to remove entries older than 5 minutes.
    /// </summary>
    public void Cleanup()
    {
        var cutoff = DateTime.UtcNow.AddMinutes(-5);
        var keysToRemove = _lastRequestTimes
            .Where(kvp => kvp.Value < cutoff)
            .Select(kvp => kvp.Key)
            .ToList();

        foreach (var key in keysToRemove)
        {
            _lastRequestTimes.TryRemove(key, out _);
        }
    }
}
