document.addEventListener('DOMContentLoaded', function() {
    setupEventListeners();
    loadErrorDetails();
});

function setupEventListeners() {
    const showDetailsBtn = document.getElementById('showDetails');
    const errorDetails = document.getElementById('errorDetails');

    if (showDetailsBtn && errorDetails) {
        showDetailsBtn.addEventListener('click', function() {
            const isHidden = errorDetails.classList.contains('hidden');

            if (isHidden) {
                errorDetails.classList.remove('hidden');
                showDetailsBtn.innerHTML = '<i class="fas fa-eye-slash"></i> Hide Technical Details';
            } else {
                errorDetails.classList.add('hidden');
                showDetailsBtn.innerHTML = '<i class="fas fa-info-circle"></i> Show Technical Details';
            }
        });
    }

    const requestBtn = document.getElementById('requestNewInvitation');
    const modal = document.getElementById('requestInvitationModal');
    const closeBtn = document.getElementById('closeModal');

    if (requestBtn && modal) {
        requestBtn.addEventListener('click', function() {
            modal.classList.remove('hidden');
        });
    }

    if (closeBtn && modal) {
        closeBtn.addEventListener('click', function() {
            modal.classList.add('hidden');
        });

        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                modal.classList.add('hidden');
            }
        });
    }

    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && modal && !modal.classList.contains('hidden')) {
            modal.classList.add('hidden');
        }
    });
}

function loadErrorDetails() {
    const urlParams = new URLSearchParams(window.location.search);
    const errorMessage = urlParams.get('error');

    if (errorMessage) {
        const errorMessageElement = document.getElementById('errorMessage');
        if (errorMessageElement) {
            const messageText = errorMessageElement.querySelector('p');
            if (messageText) {
                messageText.textContent = errorMessage;
            }
        }
    }
}
