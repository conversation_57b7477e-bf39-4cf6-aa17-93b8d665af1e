function buildRegistrationUrl() {
  const baseUrl = window.appConfig?.functionUrl;
  const functionKey = window.appConfig?.registrationFunctionKey;

  if (!baseUrl || !functionKey || functionKey.includes('ERROR_MISSING')) {
    return null;
  }

  return `${baseUrl}/api/RegistrationService?operation=register&code=${functionKey}`;
}

const APPLICATION_NAME = window.appConfig?.applicationName || "ApplicationNameNotSet";

function validateEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email.trim());
}

function validatePassword(password) {
  if (password.length < 8) return false;
  return /[A-Z]/.test(password) && /[a-z]/.test(password) && /\d/.test(password) && /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~]/.test(password);
}

function validateName(name) {
  const trimmed = name.trim();
  return trimmed.length >= 1 && trimmed.length <= 50 && /^[a-zA-Z\s'-]+$/.test(trimmed);
}

const errorMessageDiv = $('#errorMessage');
const successMessageDiv = $('#successMessage');
const registerButton = $('#registerButton');
const registrationForm = $('#registrationForm');

function showMessage(message, isError = true) {
  errorMessageDiv.addClass('d-none');
  successMessageDiv.addClass('d-none');
  const messageDiv = isError ? errorMessageDiv : successMessageDiv;
  messageDiv.html(message).removeClass('d-none');
}

function showLoadingState(message) {
  registerButton.prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> ' + message);
}

function resetLoadingState() {
  registerButton.prop('disabled', false).text('Create Account');
}

function togglePasswordVisibility(inputId, toggleButton) {
  const input = $(`#${inputId}`);
  const icon = toggleButton.find('i');

  if (input.attr('type') === 'password') {
    input.attr('type', 'text');
    icon.removeClass('fa-eye').addClass('fa-eye-slash');
  } else {
    input.attr('type', 'password');
    icon.removeClass('fa-eye-slash').addClass('fa-eye');
  }
}

async function registerUser(userData) {
  showLoadingState('Creating Account...');
  try {
    const email = userData.email.trim();
    const password = userData.password;
    const firstName = userData.firstName.trim();
    const lastName = userData.lastName.trim();
    const invitationCode = userData.invitationCode.trim();

    if (!validateEmail(email)) {
      throw new Error('Invalid email format');
    }

    if (!validatePassword(password)) {
      throw new Error('Password does not meet security requirements');
    }

    if (!validateName(firstName)) {
      throw new Error('Invalid first name');
    }

    if (!validateName(lastName)) {
      throw new Error('Invalid last name');
    }

    if (!invitationCode || invitationCode.length < 6) {
      throw new Error('Valid invitation code is required');
    }

    const registrationUrl = buildRegistrationUrl();
    if (!registrationUrl) {
      throw new Error('Azure Function configuration missing. Please check Power Pages settings.');
    }

    const response = await fetch(registrationUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Client-Version': '3.0.0-simplified',
        'X-Requested-With': 'XMLHttpRequest'
      },
      body: JSON.stringify({
        Email: email,
        Password: password,
        FirstName: firstName,
        LastName: lastName,
        ApplicationName: APPLICATION_NAME,
        Token: invitationToken,
        VerificationCode: invitationCode
      })
    });

    if (response.status === 429) {
      throw new Error('Too many requests. Please wait a moment before trying again.');
    }

    const result = await response.json();

    if (!response.ok || result.success === false) {
      throw new Error(result.message || "Registration failed. Please try again.");
    }

    return { success: true, message: result.message || "Account created successfully!" };
  } catch (error) {
    throw error;
  } finally {
    resetLoadingState();
  }
}

// Client-side form validation
function validateRegistrationForm() {
  let isValid = true;
  
  // Clear previous validation states
  $('.form-control').removeClass('is-invalid');
  $('.invalid-feedback').text('');

  // Email validation
  const email = $('#email').val();
  if (!validateEmail(email)) {
    $('#emailError').text('Please enter a valid email address');
    $('#email').addClass('is-invalid');
    isValid = false;
  }

  // First name validation
  const firstName = $('#firstName').val();
  if (!validateName(firstName)) {
    $('#firstNameError').text('Please enter a valid first name (letters, spaces, hyphens, and apostrophes only)');
    $('#firstName').addClass('is-invalid');
    isValid = false;
  }

  // Last name validation
  const lastName = $('#lastName').val();
  if (!validateName(lastName)) {
    $('#lastNameError').text('Please enter a valid last name (letters, spaces, hyphens, and apostrophes only)');
    $('#lastName').addClass('is-invalid');
    isValid = false;
  }

  // Password validation
  const password = $('#password').val();
  if (!validatePassword(password)) {
    $('#passwordError').text('Password must meet complexity requirements');
    $('#password').addClass('is-invalid');
    isValid = false;
  }

  // Confirm password validation
  const confirmPassword = $('#confirmPassword').val();
  if (password !== confirmPassword) {
    $('#confirmPasswordError').text('Passwords do not match');
    $('#confirmPassword').addClass('is-invalid');
    isValid = false;
  }

  // Terms acceptance validation
  const invitationCode = $('#invitationCode').val();
  if (!invitationCode || invitationCode.trim().length < 6) {
    $('#invitationCodeError').text('Please enter a valid invitation code');
    $('#invitationCode').addClass('is-invalid');
    isValid = false;
  }

  const termsAccepted = $('#termsAccepted').is(':checked');
  if (!termsAccepted) {
    $('#termsError').text('You must accept the terms and conditions');
    $('#termsAccepted').addClass('is-invalid');
    isValid = false;
  }

  return isValid;
}

// Event Handlers
function initializeFormHandlers() {
  // Password visibility toggles
  if (toggleButtons.password && toggleButtons.password.length) {
    toggleButtons.password.click(() => togglePasswordVisibility('password', toggleButtons.password));
  }
  if (toggleButtons.confirm && toggleButtons.confirm.length) {
    toggleButtons.confirm.click(() => togglePasswordVisibility('confirmPassword', toggleButtons.confirm));
  }

  // Form submit handler
  registrationForm.submit(async function(event) {
    event.preventDefault();
    showMessage('', false); // Clear previous messages

    try {
      // Client-side validation
      if (!validateRegistrationForm()) {
        return;
      }

      // Collect form data
      const userData = {
        email: $('#email').val(),
        password: $('#password').val(),
        firstName: $('#firstName').val(),
        lastName: $('#lastName').val(),
        invitationCode: $('#invitationCode').val()
      };

      // Register user
      const result = await registerUser(userData);
      
      if (result.success) {
        showMessage("Account created successfully! Redirecting to home page...", false);
        registrationForm[0].reset();

        setTimeout(() => {
          sessionStorage.setItem('registeredEmail', userData.email);
          sessionStorage.setItem('justRegistered', 'true');
          window.location.href = '/';
        }, 2000);
      }

    } catch (error) {
      showMessage(error.message || "An unexpected error occurred. Please try again.");
    }
  });
}

// Enhanced Security: Validate token for page access and require manual code entry
let invitationToken = null;

function validateInvitationTokenAndExtractCode() {
  const urlParams = new URLSearchParams(window.location.search);
  invitationToken = urlParams.get('token');
  const invitationCode = urlParams.get('code');
  
  // First, validate the token for page access
  if (!invitationToken) {
    showUnauthorizedAccess('No invitation token provided. Please use the link from your invitation email.');
    return;
  }
  
  // Validate token with server
  validateTokenAccess(invitationToken);
  
  if (invitationCode) {
    displayCodeReference(invitationCode);
  }
}

function buildInvitationValidationUrl() {
  const baseUrl = window.appConfig?.functionUrl;
  const functionKey = window.appConfig?.invitationFunctionKey;

  if (!baseUrl || !functionKey || functionKey.includes('ERROR_MISSING')) {
    return null;
  }

  return `${baseUrl}/api/InvitationService?operation=validate-token&code=${functionKey}`;
}

// Validate token access with server
async function validateTokenAccess(token) {
  try {
    const invitationValidationUrl = buildInvitationValidationUrl();
    if (!invitationValidationUrl) {
      throw new Error('Azure Function configuration missing. Please check Power Pages settings.');
    }

    const response = await fetch(invitationValidationUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        Token: token
      })
    });

    const result = await response.json();

    if (!result.success) {
      showUnauthorizedAccess(result.message || 'Invalid or expired invitation token.');
      return;
    }

    showTokenValidationSuccess();

  } catch (error) {
    showUnauthorizedAccess('Unable to validate invitation token. Please try again.');
  }
}

// Display verification code for user reference (don't auto-fill)
function displayCodeReference(code) {
  const codeDisplay = document.createElement('div');
  codeDisplay.className = 'alert alert-info mt-3';
  codeDisplay.innerHTML = `
    <h6><i class="fas fa-info-circle"></i> Your Verification Code</h6>
    <p class="mb-2">Enter this code in the form below:</p>
    <div class="code-display" style="font-family: monospace; font-size: 1.2em; font-weight: bold; color: #0066cc;">${code}</div>
    <small class="text-muted">This code was provided in your invitation email.</small>
  `;
  
  // Insert before the form
  const form = document.getElementById('registrationForm');
  if (form && form.parentNode) {
    form.parentNode.insertBefore(codeDisplay, form);
  }
}

// Show unauthorized access - redirect to error page
function showUnauthorizedAccess(message) {
  console.error('Access denied:', message);
  
  // Store error details for the error page
  sessionStorage.setItem('invitationError', JSON.stringify({
    message: message,
    timestamp: new Date().toISOString(),
    source: 'registration-token-validation'
  }));
  
  // Redirect to error page (or home page if no error page exists)
  // You can create a dedicated error page in Power Pages for better UX
  const errorPageUrl = '/Invitation-Error/'; // Change this to your error page URL
  
  // Try to redirect to error page, fallback to home
  try {
    window.location.href = errorPageUrl;
  } catch (error) {
    console.warn('Error page not found, redirecting to home:', error);
    window.location.href = '/';
  }
}

// Show token validation success
function showTokenValidationSuccess() {
  const pageHeader = document.querySelector('.card-header h3');
  if (pageHeader) {
    pageHeader.innerHTML = '<i class="fas fa-check-circle text-success"></i> Create Account <small class="text-success">(Invitation Verified)</small>';
  }
}

// Initialize the application when the document is ready
$(document).ready(function() {
  initializeFormHandlers();
  validateInvitationTokenAndExtractCode();
});
