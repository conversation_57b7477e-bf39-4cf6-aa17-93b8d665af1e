using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using System.Net;
using System.Text.Json;
using PasswordHistoryValidator.Services;

namespace PasswordHistoryValidator.Shared;

public abstract class BaseFunctionService
{
    protected readonly JsonSerializerOptions JsonOptions;

    protected BaseFunctionService(JsonSerializerOptions jsonOptions)
    {
        JsonOptions = jsonOptions ?? throw new ArgumentNullException(nameof(jsonOptions));
    }

    #region Response Methods

    protected async Task<HttpResponseData> CreateSuccessResponse<T>(HttpRequestData req, string message, T? data = default, HttpStatusCode statusCode = HttpStatusCode.OK, string? correlationId = null)
    {
        var response = req.CreateResponse(statusCode);

        // Add metadata to headers
        response.Headers.Add("X-Correlation-ID", correlationId ?? GenerateCorrelationId());
        response.Headers.Add("X-Timestamp", DateTime.UtcNow.ToString("O"));
        response.Headers.Add("Content-Type", "application/json");

        // Create consistent success response structure
        var result = new
        {
            success = true,
            message = message,
            data = data
        };

        var json = JsonSerializer.Serialize(result, JsonOptions);
        await response.WriteStringAsync(json);
        AddCorsHeaders(response);
        return response;
    }

    protected async Task<HttpResponseData> CreateErrorResponse(HttpRequestData req, string message, string? errorCode = null, HttpStatusCode statusCode = HttpStatusCode.BadRequest, string? correlationId = null)
    {
        var response = req.CreateResponse(statusCode);

        // Add metadata to headers
        response.Headers.Add("X-Correlation-ID", correlationId ?? GenerateCorrelationId());
        response.Headers.Add("X-Timestamp", DateTime.UtcNow.ToString("O"));
        response.Headers.Add("Content-Type", "application/json");

        // Create consistent error response structure
        var result = new
        {
            success = false,
            message = message,
            errorCode = errorCode
        };

        var json = JsonSerializer.Serialize(result, JsonOptions);
        await response.WriteStringAsync(json);
        AddCorsHeaders(response);
        return response;
    }

    // Legacy method for backward compatibility during transition
    [Obsolete("Use CreateSuccessResponse instead")]
    protected async Task<HttpResponseData> CreateJsonResponse<T>(HttpRequestData req, T data, HttpStatusCode statusCode, string correlationId)
    {
        // For now, delegate to new method to maintain compatibility
        return await CreateSuccessResponse(req, "Operation completed", data, statusCode, correlationId);
    }

    #endregion

    #region CORS and Common Utilities

    protected virtual void AddCorsHeaders(HttpResponseData response)
    {
        // Add CORS headers for cross-origin requests
        response.Headers.Add("Access-Control-Allow-Origin", "*");
        response.Headers.Add("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
        response.Headers.Add("Access-Control-Allow-Headers", "Content-Type, Authorization, x-functions-key, X-Requested-With, X-Client-Version");
        response.Headers.Add("Access-Control-Max-Age", "86400");
    }


    protected HttpResponseData CreateCorsResponse(HttpRequestData req)
    {
        var corsResponse = req.CreateResponse(HttpStatusCode.OK);
        AddCorsHeaders(corsResponse);
        return corsResponse;
    }

    protected static string GenerateCorrelationId() => Guid.NewGuid().ToString();

    protected static string GetClientIdentifier(HttpRequestData req)
    {
        var clientIp = req.Headers.GetValues("X-Forwarded-For").FirstOrDefault()?.Split(',').FirstOrDefault()?.Trim()
                      ?? req.Headers.GetValues("X-Real-IP").FirstOrDefault()
                      ?? req.Headers.GetValues("CF-Connecting-IP").FirstOrDefault()
                      ?? "unknown";

        return $"ip:{clientIp}";
    }

    #endregion
}
