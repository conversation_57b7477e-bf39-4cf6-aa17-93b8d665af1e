# Disabling Sign-up in Entra External ID for Power Pages

**Date Created:** July 31, 2025  
**Issue:** Power Pages with Entra External ID shows "No account? Create one" option on sign-in page  
**Goal:** Make the site invite-only by removing self-service registration  

## Problem Description

When using Power Pages with Entra External ID as the identity provider, the standard sign-in page displays a "No account? Create one" link that allows users to self-register. For invite-only applications, this needs to be disabled.

### Current Configuration
- **Power Pages Site:** https://site-dccs0.powerappsportals.com/
- **Entra External ID Tenant:** EntraExternalTestOsler.onmicrosoft.com
- **Application ID:** 057ea321-fbdb-409b-96a2-b10186ad9aca
- **Authority URL:** https://EntraExternalTestOsler.ciamlogin.com/c0a19123-40d6-43e2-be53-aafde4...
- **Redirect URI:** https://site-dccs0.powerappsportals.com/signin-openid_2

## Solution Overview

According to Microsoft documentation, the sign-up functionality can be disabled by updating the user flow's `isSignUpAllowed` property to `false` using the Microsoft Graph API.

### Required API Call
```http
PATCH https://graph.microsoft.com/beta/identity/authenticationEventsFlows/{user-flow-id}
Content-Type: application/json

{    
    "@odata.type": "#microsoft.graph.externalUsersSelfServiceSignUpEventsFlow",    
    "onInteractiveAuthFlowStart": {    
        "@odata.type": "#microsoft.graph.onInteractiveAuthFlowStartExternalUsersSelfServiceSignUp",    
        "isSignUpAllowed": false    
    }    
}
```

## Attempted Solutions

### 1. Microsoft Graph Explorer
**Status:** ❌ Failed - Permission Issues

**Steps Tried:**
1. Accessed https://developer.microsoft.com/en-us/graph/graph-explorer
2. Signed in with admin account
3. Attempted GET request to find user flow ID:
   ```
   https://graph.microsoft.com/beta/identity/authenticationEventsFlows?$filter=conditions/applications/any(x:x/appId eq '057ea321-fbdb-409b-96a2-b10186ad9aca')
   ```

**Error Received:**
```
Forbidden - 403 - Either the signed-in user does not have sufficient privileges, or you need to consent to one of the permissions on the Modify permissions tab
```

**Required Permission:** `EventListener.ReadWrite.All`  
**Issue:** Permission consent did not resolve the access issue

### 2. PowerShell with Microsoft Graph Module
**Status:** ❌ Failed - Multiple Issues

**Steps Tried:**
1. Installed Microsoft Graph PowerShell module:
   ```powershell
   Install-Module Microsoft.Graph -Force
   Connect-MgGraph -Scopes "Policy.ReadWrite.AuthenticationFlows"
   ```

2. Attempted specific cmdlet:
   ```powershell
   Get-MgIdentityAuthenticationEventsFlow -Filter "conditions/applications/any(x:x/appId eq '057ea321-fbdb-409b-96a2-b10186ad9aca')"
   ```
   **Error:** Cmdlet not recognized

3. Attempted generic Graph request with incorrect filter syntax:
   ```powershell
   Invoke-MgGraphRequest -Method GET -Uri "https://graph.microsoft.com/beta/identity/authenticationEventsFlows?`$filter=conditions/applications/any(x:x/appId eq '057ea321-fbdb-409b-96a2-b10186ad9aca')"
   ```
   **Error:** `Invalid filter clause: Any/All may only be used following a collection`

4. Attempted to get all user flows:
   ```powershell
   Invoke-MgGraphRequest -Method GET -Uri "https://graph.microsoft.com/beta/identity/authenticationEventsFlows"
   ```
   **Error:** `Unauthorized. Access to this Api requires feature: 'EnableMsGraphAuthenticationEventListener' for the tenant`

## Root Cause Analysis

The primary issue is that our Entra External ID tenant does not have the required feature `EnableMsGraphAuthenticationEventListener` enabled. This is a tenant-level feature that must be activated by Microsoft.

### Error Details
- **Tenant ID:** 56aa9300-5280-4836-88b9-5c1aef2d77c4
- **Missing Feature:** EnableMsGraphAuthenticationEventListener
- **API Endpoint:** /beta/identity/authenticationEventsFlows
- **Required Permissions:** EventListener.ReadWrite.All, Policy.ReadWrite.AuthenticationFlows

## Alternative Solutions to Investigate

### 1. Azure Portal Configuration
**Next Steps:**
1. Navigate to https://entra.microsoft.com
2. Go to **External Identities** > **User flows**
3. Select the user flow associated with application ID `057ea321-fbdb-409b-96a2-b10186ad9aca`
4. Look for sign-up/registration toggle settings in the UI

### 2. Application Registration Settings
**Next Steps:**
1. In Azure portal, go to **Microsoft Entra ID** > **App registrations**
2. Find app `057ea321-fbdb-409b-96a2-b10186ad9aca`
3. Check **Authentication** settings for sign-up related configurations
4. Review **API permissions** for any sign-up related scopes

### 3. Microsoft Support Request
**Required Actions:**
1. Contact Microsoft support to enable `EnableMsGraphAuthenticationEventListener` feature
2. Request access to beta Graph API features for Entra External ID
3. Provide tenant ID: `56aa9300-5280-4836-88b9-5c1aef2d77c4`

### 4. Custom Implementation Workaround
**Potential Options:**
1. Create custom CSS to hide the "No account? Create one" link
2. Implement JavaScript redirect logic to prevent sign-up access
3. Create a new user flow specifically for sign-in only (if available)

## References

- **Microsoft Documentation:** [Disable sign-up in a sign-up and sign-in user flow](https://learn.microsoft.com/en-us/entra/external-id/customers/how-to-user-flow-sign-up-sign-in-customers#disable-sign-up-in-a-sign-up-and-sign-in-user-flow)
- **Microsoft Q&A Thread:** [External Identity User Flows: Disabling Sign-up](https://learn.microsoft.com/en-us/answers/questions/1611622/external-identity-user-flows-disabling-sign-up-in)
- **Graph API Reference:** [Update authenticationEventsFlow](https://learn.microsoft.com/en-us/graph/api/authenticationeventsflow-update)

## Status and Next Actions

**Current Status:** ⏳ Blocked - Tenant feature not enabled  
**Priority:** High - Required for invite-only functionality  
**Owner:** Tyler  
**Next Action:** Investigate Azure portal configuration options before contacting Microsoft support  

### Immediate Next Steps
1. Check Azure portal user flow settings for UI-based sign-up disable option
2. Review application registration authentication settings
3. If no portal options available, submit Microsoft support ticket for feature enablement
4. Consider temporary CSS/JavaScript workaround if needed urgently

## Timeline Expectations

Based on Microsoft Q&A responses, once the proper API call is made successfully:
- **Propagation Time:** Few minutes to few hours due to system caching
- **Verification:** Test sign-in page after propagation period
- **Rollback:** Set `isSignUpAllowed` to `true` to re-enable if needed
