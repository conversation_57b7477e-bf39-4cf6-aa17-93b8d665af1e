function buildPasswordServiceUrl() {
  const baseUrl = window.appConfig?.functionUrl;
  const functionKey = window.appConfig?.passwordFunctionKey;

  if (!baseUrl || !functionKey || functionKey.includes('ERROR_MISSING')) {
    return null;
  }

  return `${baseUrl}/api/PasswordService?operation=reset-initiate&code=${functionKey}`;
}

const APPLICATION_NAME = window.appConfig?.applicationName || "ApplicationNameNotSet";

function validateEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email) && email.length <= 254;
}



const errorMessageDiv = $('#errorMessage');
const successMessageDiv = $('#successMessage');
const resetButton = $('#resetButton');
const forgotPasswordForm = $('#forgotPasswordForm');
const emailInput = $('#email');

function showMessage(message, isError = true, timeout = 0) {
  errorMessageDiv.hide();
  successMessageDiv.hide();
  const messageDiv = isError ? errorMessageDiv : successMessageDiv;
  messageDiv.html(message).show();
  if (timeout > 0) {
    setTimeout(() => messageDiv.fadeOut(), timeout);
  }
}

function clearMessages() {
  errorMessageDiv.hide();
  successMessageDiv.hide();
}

function validateEmailField() {
  const email = emailInput.val().trim();
  const emailError = $('#emailError');

  if (!email) {
    emailError.text('Email address is required');
    emailInput.addClass('is-invalid');
    return false;
  }

  if (!validateEmail(email)) {
    emailError.text('Please enter a valid email address');
    emailInput.addClass('is-invalid');
    return false;
  }

  emailError.text('');
  emailInput.removeClass('is-invalid');
  return true;
}

async function initiatePasswordReset(email) {
  try {
    const trimmedEmail = email.trim();

    if (!validateEmail(trimmedEmail)) {
      throw new Error('Invalid email address');
    }

    const passwordServiceUrl = buildPasswordServiceUrl();
    if (!passwordServiceUrl) {
      throw new Error('Azure Function configuration missing. Please check Power Pages settings.');
    }

    const response = await fetch(passwordServiceUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Client-Version': '3.0.0-simplified',
        'X-Requested-With': 'XMLHttpRequest'
      },
      body: JSON.stringify({
        Email: trimmedEmail,
        ApplicationName: APPLICATION_NAME
      })
    });

    if (response.status === 429) {
      throw new Error('Too many requests. Please wait a moment before trying again.');
    }

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.message || "Failed to send reset link. Please try again.");
    }

    // For security, always show generic message regardless of whether user exists
    return {
      success: true,
      message: "If an account with that email exists, you will receive a reset link shortly."
    };

  } catch (error) {
    throw error;
  }
}

function initializeFormHandlers() {
  emailInput.blur(function() {
    validateEmailField();
  });

  emailInput.on('input', function() {
    $(this).removeClass('is-invalid');
    $('#emailError').text('');
  });

  forgotPasswordForm.submit(async function(event) {
    event.preventDefault();
    clearMessages();

    try {
      if (!validateEmailField()) {
        return;
      }

      resetButton.prop("disabled", true);
      resetButton.text("Sending...");

      showMessage("Sending reset link...", false);

      const email = emailInput.val().trim();
      const result = await initiatePasswordReset(email);

      if (result.success) {
        showMessage(result.message, false);
        forgotPasswordForm[0].reset();
        emailInput.removeClass('is-invalid');
        $('#emailError').text('');
      }

    } catch (error) {
      showMessage(error.message || "An unexpected error occurred. Please try again.");
    } finally {
      resetButton.prop("disabled", false);
      resetButton.text("Send Reset Link");
    }
  });
}

// Initialize the application when the document is ready
$(document).ready(function() {
  initializeFormHandlers();
});
