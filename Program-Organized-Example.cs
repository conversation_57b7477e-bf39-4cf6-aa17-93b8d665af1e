using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Azure.Identity;
using PasswordHistoryValidator.Extensions;

var host = new HostBuilder()
    .ConfigureFunctionsWorkerDefaults()
    .ConfigureServices((context, services) =>
    {
        var configuration = context.Configuration;

        // Logging configuration
        services.AddLogging(builder =>
        {
            builder.AddConsole();
            builder.AddApplicationInsights();
        });

        // Register configuration as singleton
        services.AddSingleton<IConfiguration>(configuration);

        // Register services in organized groups
        services.AddConfigurationOptions(configuration);
        services.AddInfrastructureServices();
        services.AddAzureServices();
        services.AddBusinessServices();
        services.AddFunctionServices();
    })
    .ConfigureAppConfiguration((context, config) =>
    {
        config.AddJsonFile("local.settings.json", optional: true, reloadOnChange: true);
        config.AddEnvironmentVariables();
        var keyVaultUrl = Environment.GetEnvironmentVariable("KeyVaultUrl");
        if (!string.IsNullOrEmpty(keyVaultUrl))
        {
            config.AddAzureKeyVault(new Uri(keyVaultUrl), new DefaultAzureCredential());
        }
    })
    .Build();

host.Run();
